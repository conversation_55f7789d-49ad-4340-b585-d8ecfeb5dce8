{"name": "<PERSON><PERSON><PERSON>", "version": "1.0.0", "type": "module", "main": "index.js", "scripts": {"test": "playwright test", "test:ui": "playwright test --ui", "test:debug": "playwright test --debug", "test:headed": "playwright test --headed --project=chromium", "test:login": "playwright test tests/login.spec.js", "test:login:ui": "playwright test tests/login.spec.js --ui", "report": "playwright show-report", "install-browsers": "playwright install", "create-test": "npx tsx create_test.ts", "record": "playwright codegen https://wify-tms-demo.wify.co.in", "test-demo": "cross-env ENV=demo playwright test --project=chromium", "test-demo-headed": "cross-env ENV=demo playwright test --headed --project=chromium", "test-qa01": "cross-env ENV=qa01 playwright test --project=chromium", "test-qa01-headed": "cross-env ENV=qa01 playwright test tests --headed --project=chromium", "test-dashboard-headed": "cross-env ENV=qa02 playwright test tests/Dashboard --headed --project=chromium", "test-qa02": "cross-env ENV=qa02 playwright test --project=chromium", "test-qa02-headed": "cross-env ENV=qa02 playwright test --headed --project=chromium", "test-qa03": "cross-env ENV=qa03 playwright test --project=chromium", "test-qa03-headed": "cross-env ENV=qa03 playwright test --headed --project=chromium", "test-qa04": "cross-env ENV=qa04 playwright test --project=chromium", "test-qa04-headed": "cross-env ENV=qa04 playwright test --headed --project=chromium", "test-qa05": "cross-env ENV=qa05 playwright test --project=chromium", "test-qa05-headed": "cross-env ENV=qa05 playwright test --headed --project=chromium"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@playwright/test": "^1.53.2", "@types/node": "^24.1.0", "cross-env": "^10.0.0", "dotenv": "^17.2.1", "tsx": "^4.7.0", "typescript": "^5.3.0"}}