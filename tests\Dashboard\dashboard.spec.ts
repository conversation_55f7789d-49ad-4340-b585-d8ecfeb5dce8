import { test, expect } from '@playwright/test';
import DashboardTestData from './data';

test('Dashboard navigation and tab interactions', async ({ page }) => {
  const { loginData, selectors } = DashboardTestData;
  
  // Login process
  await page.goto(loginData.baseUrl);
  await page.getByRole(selectors.emailInput.role, { name: selectors.emailInput.name }).click();
  await page.getByRole(selectors.emailInput.role, { name: selectors.emailInput.name }).fill(loginData.email);
  await page.getByRole(selectors.passwordInput.role, { name: selectors.passwordInput.name }).click();
  await page.getByRole(selectors.passwordInput.role, { name: selectors.passwordInput.name }).fill(loginData.password);
  await page.getByRole(selectors.signInButton.role, { name: selectors.signInButton.name }).click();
  
  // Navigate to dashboard
  await page.locator(selectors.dashboardTile).first().click();
  
  // Navigate between admin and user views
  await page.getByRole(selectors.adminViewLink.role, { name: selectors.adminViewLink.name }).click();
  await page.getByRole(selectors.userViewLink.role, { name: selectors.userViewLink.name }).click();
  
  // Navigate through main tabs
  await page.getByRole(selectors.tabs.statusGroup.role, { name: selectors.tabs.statusGroup.name }).click();
  await page.getByLabel('Status Group').getByText(selectors.tabContent.statusGroup).click();
  
  await page.getByRole(selectors.tabs.serviceRequests.role, { name: selectors.tabs.serviceRequests.name }).click();
  await page.getByLabel('Service Requests').getByText(selectors.tabContent.serviceRequests).click();
  
  await page.getByRole(selectors.tabs.zone.role, { name: selectors.tabs.zone.name }).click();
  await page.getByLabel('Zone').getByText(selectors.tabContent.zone, { exact: true }).click();
  
  await page.getByRole(selectors.tabs.locationGroup.role, { name: selectors.tabs.locationGroup.name }).click();
  await page.getByText(selectors.tabContent.locationGroup, { exact: true }).click();
  
  await page.getByRole(selectors.tabs.customFields.role, { name: selectors.tabs.customFields.name }).click();
  await page.getByLabel('Custom Fields').getByText(selectors.tabContent.customFields).click();
  
  await page.getByRole(selectors.tabs.ageingReport.role, { name: selectors.tabs.ageingReport.name }).click();
  await page.getByLabel('Ageing Report').getByText(selectors.tabContent.ageingReport).click();
  
  await page.getByRole(selectors.tabs.authorityWiseRequest.role, { name: selectors.tabs.authorityWiseRequest.name }).click();
  await page.getByText(selectors.tabContent.authorityWiseRequest).click();
  
  await page.getByRole(selectors.tabs.customerFeedback.role, { name: selectors.tabs.customerFeedback.name }).click();
  await page.getByRole('heading', { name: selectors.tabContent.customerFeedback }).locator('span').click();
  
  // Test dropdown interactions
  await page.locator(selectors.dropdown.moreButton).click();
  await page.getByLabel(selectors.dropdown.expandedDropdown).getByText(selectors.dropdown.statusGroupOption).click();
  await page.getByLabel('Status Group', { exact: true }).getByText(selectors.dropdown.statusGroupOption).click();
  
  await page.locator(selectors.dropdown.moreButton).click();
  await page.getByLabel(selectors.dropdown.expandedDropdown).getByText(selectors.dropdown.recentRequestsOption).click();
  await page.getByLabel('Recent Requests').getByText(selectors.dropdown.recentRequestsOption).click();
  
  await page.locator(selectors.dropdown.moreButton).click();
  await page.getByRole(selectors.dropdown.customerFeedbackOption.role, { name: selectors.dropdown.customerFeedbackOption.name }).click();
  await page.getByRole('heading', { name: selectors.tabContent.customerFeedback }).locator('span').click();
});
