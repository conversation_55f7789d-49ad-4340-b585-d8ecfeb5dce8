// tests/RequestCreation/requestCreation.spec.ts
import { test, expect } from '@playwright/test';
import RequestCreationData from './data';

test('Request creation via Project based flow', async ({ page ,baseURL}) => {
  const {
    auth,
    selectors: {
      dashboardTile,
      projectBasedLink,
      addButton,
      mobileInput,
      nameInputA,
      nameInputB,
      descriptionInput,
      priorityCombo,
      priorityNormalTitle,
      submitButton,
    },
    testData: { mobile, nameWhenA, nameWhenB, description },
  } = RequestCreationData;

  // Sign in (kept inline as per your original test)
  await page.goto(`${baseURL}/signin`);
  await page.getByRole('textbox', { name: 'Email' }).fill(auth.email);
  await page.getByRole('textbox', { name: 'Password' }).fill(auth.password);
  await page.getByRole('button', { name: 'Sign In' }).click();

  // Navigate to Project based > add
  await page.locator(dashboardTile).first().click();
  await page.getByRole(projectBasedLink.role, { name: projectBasedLink.name }).click();
  await page.getByRole(addButton.role, { name: addButton.name }).click();

  // Fill mobile
  await page.getByRole(mobileInput.role, { name: mobileInput.name }).fill(mobile);
  await page.waitForTimeout(3000);

  // Handle either name input variant
  const nameA = page.getByRole(nameInputA.role, { name: nameInputA.name });
  const nameB = page.getByRole(nameInputB.role, { name: nameInputB.name });

  const appeared = await Promise.race([
    nameA.waitFor({ state: 'visible', timeout: 4000 }).then(() => 'A').catch(() => null),
    nameB.waitFor({ state: 'visible', timeout: 4000 }).then(() => 'B').catch(() => null),
  ]);

  if (appeared === 'A') {
    await nameA.click();
    await nameA.fill(nameWhenA);
  } else if (appeared === 'B') {
    await nameB.click();
    await nameB.fill(nameWhenB);
  } else {
    throw new Error('Neither name textbox appeared');
  }

  // Description, priority, submit
  await page.getByRole(descriptionInput.role, { name: descriptionInput.name }).fill(description);
  await page.getByRole(priorityCombo.role, { name: priorityCombo.name }).click();
  await page.getByTitle(priorityNormalTitle).click();
  await page.getByRole(submitButton.role, { name: submitButton.name }).click();

  // Wait for form submission to complete and verify success
  // try {
  //   // Option 1: Check for success message (most reliable)
  //   await expect(page.getByText('Request created')).toBeVisible({ timeout: 15000 });
  // } catch (error) {
  //   // Option 2: Check if we're redirected to a list page with our request
  //   await expect(page.getByText('PLAWRIGHT 102').first()).toBeVisible({ timeout: 15000 });
  // }
});
