import { test, expect } from '@playwright/test';

test('test', async ({ page }) => {
  await page.goto('https://qa02.wify.co.in/signin');
  await page.getByRole('textbox', { name: 'Email' }).click();
  await page.getByRole('textbox', { name: 'Email' }).fill('<EMAIL>');
  await page.getByRole('textbox', { name: 'Password' }).click();
  await page.getByRole('textbox', { name: 'Password' }).fill('test');
  await page.getByRole('button', { name: 'Sign In' }).click();
  await page.getByRole('heading', { name: 'Test environment', exact: true }).locator('span').click();
  await page.getByRole('link', { name: 'e Order' }).click();
  await page.getByText('OpenORDE250904358117(Normal)').nth(1).click();
  await page.getByRole('button', { name: 'user-add Add subtask' }).click();
  await page.getByText('Visit', { exact: true }).click();
  await page.getByRole('textbox', { name: '* zStart date ( No requested' }).click();
  await page.getByText('Today').click();
  await page.getByRole('combobox', { name: '* Start Time' }).click();
  await page.getByTitle(':00PM').locator('div').click();
  await page.getByRole('combobox', { name: '* End Time' }).click();
  await page.getByLabel('Request Details').getByText(':00PM').click();
  await page.getByText('1:15PM').nth(1).click();
  await page.getByRole('combobox', { name: '* End Time' }).click();
  await page.getByText('1:30PM').nth(3).click();
  await page.getByRole('switch', { name: 'Show All' }).click();
  await page.locator('div:nth-child(8) > .ant-col.ant-col-18 > .ant-row > .ant-col.ant-form-item-control > .ant-form-item-control-input > .ant-form-item-control-input-content > .ant-select > .ant-select-selector > .ant-select-selection-overflow').click();
  await page.getByText('Roy17 (Roy17)').click();
  await page.getByText('PriorityNormalRemarks').click();
  await page.getByRole('textbox', { name: '* Remarks' }).click();
  await page.getByRole('textbox', { name: '* Remarks' }).fill('test');
  await page.getByRole('button', { name: 'Create' }).click();
});