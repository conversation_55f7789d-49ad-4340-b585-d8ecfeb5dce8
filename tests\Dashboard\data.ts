/**
 * Test data for Dashboard functionality
 */

interface DashboardTestData {
  loginData: {
    email: string;
    password: string;
    baseUrl: string;
  };
  selectors: {
    // Login selectors
    emailInput: { role: 'textbox'; name: string };
    passwordInput: { role: 'textbox'; name: string };
    signInButton: { role: 'button'; name: string };
    dashboardTile: string;
    
    // Navigation links
    adminViewLink: { role: 'link'; name: string };
    userViewLink: { role: 'link'; name: string };
    
    // Tab selectors
    tabs: {
      statusGroup: { role: 'tab'; name: string };
      serviceRequests: { role: 'tab'; name: string };
      zone: { role: 'tab'; name: string };
      locationGroup: { role: 'tab'; name: string };
      customFields: { role: 'tab'; name: string };
      ageingReport: { role: 'tab'; name: string };
      authorityWiseRequest: { role: 'tab'; name: string };
      customerFeedback: { role: 'tab'; name: string };
    };
    
    // Tab content selectors
    tabContent: {
      statusGroup: string;
      serviceRequests: string;
      zone: string;
      locationGroup: string;
      customFields: string;
      ageingReport: string;
      authorityWiseRequest: string;
      customerFeedback: string;
    };
    
    // Dropdown selectors
    dropdown: {
      moreButton: string;
      expandedDropdown: string;
      statusGroupOption: string;
      recentRequestsOption: string;
      customerFeedbackOption: { role: 'option'; name: string };
    };
  };
}

const DashboardTestData: DashboardTestData = {
  loginData: {
    email: '<EMAIL>',
    password: 'test',
    baseUrl: 'https://qa02.wify.co.in/signin',
  },
  selectors: {
    // Login selectors
    emailInput: { role: 'textbox', name: 'Email' },
    passwordInput: { role: 'textbox', name: 'Password' },
    signInButton: { role: 'button', name: 'Sign In' },
    dashboardTile: 'div:nth-child(2) > .ant-col > .ant-list-item > .ant-list-item-meta > .ant-list-item-meta-content > .ant-list-item-meta-title > .gx-position-relative > .wy-gap-0 > div',
    
    // Navigation links
    adminViewLink: { role: 'link', name: ' Admin view' },
    userViewLink: { role: 'link', name: ' User view' },
    
    // Tab selectors
    tabs: {
      statusGroup: { role: 'tab', name: 'Status Group' },
      serviceRequests: { role: 'tab', name: 'Service Requests' },
      zone: { role: 'tab', name: 'Zone' },
      locationGroup: { role: 'tab', name: 'Location group' },
      customFields: { role: 'tab', name: 'Custom Fields' },
      ageingReport: { role: 'tab', name: 'Ageing Report' },
      authorityWiseRequest: { role: 'tab', name: 'Authority Wise Request' },
      customerFeedback: { role: 'tab', name: 'Customer Feedback' },
    },
    
    // Tab content selectors
    tabContent: {
      statusGroup: 'Status Group',
      serviceRequests: 'Service Requests',
      zone: 'Zone',
      locationGroup: 'Location Group',
      customFields: 'Custom Fields',
      ageingReport: 'Ageing Report',
      authorityWiseRequest: 'Authority Wise Service Request',
      customerFeedback: 'Customer Feedback ( Created',
    },
    
    // Dropdown selectors
    dropdown: {
      moreButton: '#rc-tabs-1-more',
      expandedDropdown: 'expanded dropdown',
      statusGroupOption: 'Status Group',
      recentRequestsOption: 'Recent Requests',
      customerFeedbackOption: { role: 'option', name: 'Customer Feedback' },
    },
  },
};

export default DashboardTestData;
