import { test, expect } from '@playwright/test';
import DashboardTestData from './data';

test('Dashboard navigation and tab interactions', async ({ page }) => {
  const { loginData, selectors } = DashboardTestData;
  
  // Login process
  await page.goto(loginData.baseUrl);
  await page.getByRole(selectors.emailInput.role, { name: selectors.emailInput.name }).click();
  await page.getByRole(selectors.emailInput.role, { name: selectors.emailInput.name }).fill(loginData.email);
  await page.getByRole(selectors.passwordInput.role, { name: selectors.passwordInput.name }).click();
  await page.getByRole(selectors.passwordInput.role, { name: selectors.passwordInput.name }).fill(loginData.password);
  await page.getByRole(selectors.signInButton.role, { name: selectors.signInButton.name }).click();
  
  // Navigate to dashboard
  await page.locator(selectors.dashboardTile).first().click();
  
  // Navigate between admin and user views
  await page.getByRole(selectors.adminViewLink.role, { name: selectors.adminViewLink.name }).click();
  await page.getByRole(selectors.userViewLink.role, { name: selectors.userViewLink.name }).click();
  
  // Navigate through main tabs with assertions
  await page.getByRole(selectors.tabs.statusGroup.role, { name: selectors.tabs.statusGroup.name }).click();
  await expect(page.getByLabel('Status Group')).toBeVisible();
  await page.getByLabel('Status Group').getByText(selectors.tabContent.statusGroup).click();

  await page.getByRole(selectors.tabs.serviceRequests.role, { name: selectors.tabs.serviceRequests.name }).click();
  await expect(page.getByLabel('Service Requests')).toBeVisible();
  await page.getByLabel('Service Requests').getByText(selectors.tabContent.serviceRequests).click();

  await page.getByRole(selectors.tabs.zone.role, { name: selectors.tabs.zone.name }).click();
  await expect(page.getByLabel('Zone')).toBeVisible();
  await page.getByLabel('Zone').getByText(selectors.tabContent.zone, { exact: true }).click();

  await page.getByRole(selectors.tabs.locationGroup.role, { name: selectors.tabs.locationGroup.name }).click();
  await expect(page.getByText(selectors.tabContent.locationGroup, { exact: true })).toBeVisible();
  await page.getByText(selectors.tabContent.locationGroup, { exact: true }).click();

  await page.getByRole(selectors.tabs.customFields.role, { name: selectors.tabs.customFields.name }).click();
  await expect(page.getByLabel('Custom Fields')).toBeVisible();
  await page.getByLabel('Custom Fields').getByText(selectors.tabContent.customFields).click();

  await page.getByRole(selectors.tabs.ageingReport.role, { name: selectors.tabs.ageingReport.name }).click();
  await expect(page.getByLabel('Ageing Report')).toBeVisible();
  await page.getByLabel('Ageing Report').getByText(selectors.tabContent.ageingReport).click();

  await page.getByRole(selectors.tabs.authorityWiseRequest.role, { name: selectors.tabs.authorityWiseRequest.name }).click();
  await expect(page.getByText(selectors.tabContent.authorityWiseRequest)).toBeVisible();
  await page.getByText(selectors.tabContent.authorityWiseRequest).click();

  await page.getByRole(selectors.tabs.customerFeedback.role, { name: selectors.tabs.customerFeedback.name }).click();
  await expect(page.getByRole('heading', { name: selectors.tabContent.customerFeedback })).toBeVisible();
  await page.getByRole('heading', { name: selectors.tabContent.customerFeedback }).locator('span').click();
  
  // Test dropdown interactions with assertions
  await page.locator(selectors.dropdown.moreButton).click();
  await expect(page.getByLabel(selectors.dropdown.expandedDropdown)).toBeVisible();
  await page.getByLabel(selectors.dropdown.expandedDropdown).getByText(selectors.dropdown.statusGroupOption).click();
  await expect(page.getByLabel('Status Group', { exact: true })).toBeVisible();
  await page.getByLabel('Status Group', { exact: true }).getByText(selectors.dropdown.statusGroupOption).click();

  await page.locator(selectors.dropdown.moreButton).click();
  await expect(page.getByLabel(selectors.dropdown.expandedDropdown)).toBeVisible();
  await page.getByLabel(selectors.dropdown.expandedDropdown).getByText(selectors.dropdown.recentRequestsOption).click();
  await expect(page.getByLabel('Recent Requests')).toBeVisible();
  await page.getByLabel('Recent Requests').getByText(selectors.dropdown.recentRequestsOption).click();

  await page.locator(selectors.dropdown.moreButton).click();
  await expect(page.getByLabel(selectors.dropdown.expandedDropdown)).toBeVisible();
  await page.getByRole(selectors.dropdown.customerFeedbackOption.role, { name: selectors.dropdown.customerFeedbackOption.name }).click();
  await expect(page.getByRole('heading', { name: selectors.tabContent.customerFeedback })).toBeVisible();
  await page.getByRole('heading', { name: selectors.tabContent.customerFeedback }).locator('span').click();
});
