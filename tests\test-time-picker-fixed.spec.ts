import { test, expect } from '@playwright/test';

test('Visit creation with working time picker', async ({ page }) => {
  // Test data
  const testData = {
    startTime: '09:00AM',
    endTime: '09:30AM',
    remarks: 'test'
  };

  // Helper function to select time from dropdown - multiple strategies
  async function selectTime(comboboxName: string, timeValue: string) {
    await page.getByRole('combobox', { name: comboboxName }).click();
    await page.waitForTimeout(500); // Wait for dropdown to open
    
    // Strategy 1: Try getByTitle (most common for Ant Design)
    try {
      await page.getByTitle(timeValue).click({ timeout: 2000 });
      return;
    } catch (error) {
      console.log(`Strategy 1 failed for ${timeValue}: ${error.message}`);
    }
    
    // Strategy 2: Try getByText with first()
    try {
      await page.getByText(timeValue).first().click({ timeout: 2000 });
      return;
    } catch (error) {
      console.log(`Strategy 2 failed for ${timeValue}: ${error.message}`);
    }
    
    // Strategy 3: Try CSS selector with title attribute
    try {
      await page.locator(`[title="${timeValue}"]`).click({ timeout: 2000 });
      return;
    } catch (error) {
      console.log(`Strategy 3 failed for ${timeValue}: ${error.message}`);
    }
    
    // Strategy 4: Try Ant Design specific selector
    try {
      await page.locator(`.ant-select-item-option-content:has-text("${timeValue}")`).click({ timeout: 2000 });
      return;
    } catch (error) {
      console.log(`Strategy 4 failed for ${timeValue}: ${error.message}`);
    }
    
    // Strategy 5: Try generic list item
    try {
      await page.locator(`li:has-text("${timeValue}")`).first().click({ timeout: 2000 });
      return;
    } catch (error) {
      console.log(`Strategy 5 failed for ${timeValue}: ${error.message}`);
    }
    
    throw new Error(`All strategies failed to select time: ${timeValue}`);
  }

  // Login
  await page.goto('https://qa02.wify.co.in/signin');
  await page.getByRole('textbox', { name: 'Email' }).fill('<EMAIL>');
  await page.getByRole('textbox', { name: 'Password' }).fill('test');
  await page.getByRole('button', { name: 'Sign In' }).click();

  // Navigate to visit creation
  await page.getByRole('heading', { name: 'Test environment', exact: true }).locator('span').click();
  await page.getByRole('link', { name: 'e Order' }).click();
  await page.getByRole('heading', { name: 'test address 2' }).locator('div').click();
  await page.getByRole('button', { name: 'user-add Add subtask' }).click();
  await page.getByRole('menuitem', { name: ' Visit' }).locator('span').click();

  // Select date
  await page.locator('.ant-col.ant-col-18 > .ant-row > .ant-col.ant-form-item-control > .ant-form-item-control-input > .ant-form-item-control-input-content > .ant-picker').click();
  await page.getByText('Today').click();

  // Select times using helper function
  await selectTime('* Start Time', testData.startTime);
  await selectTime('* End Time', testData.endTime);

  // Continue with rest of form
  await page.getByRole('switch', { name: 'Show All' }).click();
  await page.locator('div:nth-child(8) > .ant-col.ant-col-18 > .ant-row > .ant-col.ant-form-item-control > .ant-form-item-control-input > .ant-form-item-control-input-content > .ant-select > .ant-select-selector > .ant-select-selection-overflow').click();
  await page.getByTitle('Bhagat.singh').locator('div').click();
  await page.getByRole('textbox', { name: '* Remarks' }).fill(testData.remarks);
  await page.getByRole('button', { name: 'Create' }).click();
  await page.getByRole('cell', { name: 'Bhagat.singh' }).click();
  await page.getByRole('button', { name: 'Save' }).click();
});
