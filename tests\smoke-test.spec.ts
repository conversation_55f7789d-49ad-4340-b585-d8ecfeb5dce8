import { test, expect } from '@playwright/test';

test.describe('Smoke Tests', () => {
  test('should display email and password input boxes on TMS login page', async ({ page }) => {
    // Navigate to the TMS login page
    await page.goto('https://tms.wify.co.in');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    
    // Verify that the email input box is visible
    const emailInput = page.getByRole('textbox', { name: /email/i });
    await expect(emailInput).toBeVisible();
    
    // Verify that the password input box is visible
    const passwordInput = page.getByRole('textbox', { name: /password/i });
    await expect(passwordInput).toBeVisible();
    
    // Optional: Verify the page title or heading to ensure we're on the right page
    await expect(page).toHaveTitle(/login|sign in|tms/i);
  });
});
