import { test, expect } from '@playwright/test';

test('test', async ({ page }) => {
  await page.goto('https://qa02.wify.co.in/signin');
  await page.getByRole('textbox', { name: 'Email' }).click();
  await page.getByRole('textbox', { name: 'Email' }).fill('<EMAIL>');
  await page.getByRole('textbox', { name: 'Password' }).click();
  await page.getByRole('textbox', { name: 'Password' }).fill('test');
  await page.getByRole('button', { name: 'Sign In' }).click();
  await page.locator('div:nth-child(2) > .ant-col > .ant-list-item > .ant-list-item-meta > .ant-list-item-meta-content > .ant-list-item-meta-title > .gx-position-relative > .wy-gap-0 > div').first().click();
  await page.getByRole('link', { name: ' Admin view' }).click();
  await page.getByRole('link', { name: ' User view' }).click();
  await page.getByRole('tab', { name: 'Status Group' }).click();
  await page.getByLabel('Status Group').getByText('Status Group').click();
  await page.getByRole('tab', { name: 'Service Requests' }).click();
  await page.getByLabel('Service Requests').getByText('Service Requests').click();
  await page.getByRole('tab', { name: 'Zone' }).click();
  await page.getByLabel('Zone').getByText('Zone', { exact: true }).click();
  await page.getByRole('tab', { name: 'Location group' }).click();
  await page.getByText('Location Group', { exact: true }).click();
  await page.getByRole('tab', { name: 'Custom Fields' }).click();
  await page.getByLabel('Custom Fields').getByText('Custom Fields').click();
  await page.getByRole('tab', { name: 'Ageing Report' }).click();
  await page.getByLabel('Ageing Report').getByText('Ageing Report').click();
  await page.getByRole('tab', { name: 'Authority Wise Request' }).click();
  await page.getByText('Authority Wise Service Request').click();
  await page.getByRole('tab', { name: 'Customer Feedback' }).click();
  await page.getByRole('heading', { name: 'Customer Feedback ( Created' }).locator('span').click();
  await page.locator('#rc-tabs-1-more').click();
  await page.getByLabel('expanded dropdown').getByText('Status Group').click();
  await page.getByLabel('Status Group', { exact: true }).getByText('Status Group').click();
  await page.locator('#rc-tabs-1-more').click();
});